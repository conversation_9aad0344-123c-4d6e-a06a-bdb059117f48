import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import Notification from '../common/Notification';
import { RotateCcw, Save, ArrowRight } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import {
  SurrenderIncomeIllustrationData,
  saveSurrenderIncomeIllustration,
  validateSurrenderIncomeData
} from '../../services/surrenderIncomeService';

const IncomePage: React.FC = () => {
  // Notification state
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);

  // Show notification temporarily
  const showNotification = (message: string, type: 'success' | 'error' = 'success') => {
    setNotification({ message, type });
  };

  // State for full surrender section
  const [fullSurrender, setFullSurrender] = useState({
    incomeByFullSurrenderEnabled: false,
    incomeByFullSurrenderAmount: '',
    modifyFullSurrenderEnabled: false,
    selectedType: '', // Only one of 'age', 'policyYear', 'calendarYear'
    ageStart: 40,
    policyYearStart: 1,
    calendarYearStart: 2024
  });

  // State for withdrawal section
  const [withdrawalModel, setWithdrawalModel] = useState({
    fixedAmountEnabled: false,
    fixedAmount: '',
    percentOfCashEnabled: false,
    percentOfCash: '',
    flatAnnualEnabled: false,
    flatAnnualAmount: '',
    modifyFlatAnnualEnabled: false,
    flatAnnualSelectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    flatAnnualAgeRange: {
      start: 40,
      end: 100
    },
    flatAnnualPolicyYearRange: {
      start: 1,
      end: 100
    },
    flatAnnualCalendarYearRange: {
      start: 2024,
      end: 2100
    },
    flatAnnualIsEditing: false,
    flatAnnualTableData: [] as TableRowData[],
    increasingIncomeEnabled: false,
    increasingStartAmount: '',
    increasingPercentage: '',
    increasingEndAge: 65,
  });

  // State for loan section
  const [loanModel, setLoanModel] = useState({
    fixedAmountEnabled: false,
    fixedAmount: '',
    percentOfCashEnabled: false,
    percentOfCash: '',
    annualAmountEnabled: false,
    annualAmount: '',
    premiumFinancingEnabled: false,
    modifyAnnualAmountEnabled: false,
    annualAmountSelectedTypes: {
      age: false,
      policyYear: false,
      calendarYear: false
    },
    annualAmountAgeRange: {
      start: 40,
      end: 100
    },
    annualAmountPolicyYearRange: {
      start: 1,
      end: 100
    },
    annualAmountCalendarYearRange: {
      start: 2024,
      end: 2100
    },
    annualAmountIsEditing: false,
    annualAmountTableData: [] as TableRowData[]
  });

  type TableRowData = {
    age: number;
    policyYear: string;
    calendarYear: number;
    amount: number;
  };

  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario, loadScenariosFromBackend } = useDashboard();

  // Save scenario state
  const [isSaving, setIsSaving] = useState(false);

  // Calculate current age from DOB - Safari compatible
  const calculateCurrentAge = (): number => {
    try {
      if (!selectedCustomerData?.details?.DOB) return 40; // Default age

      const dob = selectedCustomerData.details.DOB;
      let birthDate: Date;

      if (dob.includes('.')) {
        const dobParts = dob.split('.');
        if (dobParts.length !== 3) return 40;
        const day = parseInt(dobParts[0], 10);
        const month = parseInt(dobParts[1], 10);
        const year = parseInt(dobParts[2], 10);
        
        if (isNaN(day) || isNaN(month) || isNaN(year)) return 40;
        birthDate = new Date(year, month - 1, day);
      } else if (dob.includes('/')) {
        const dobParts = dob.split('/');
        if (dobParts.length !== 3) return 40;
        const first = parseInt(dobParts[0], 10);
        const second = parseInt(dobParts[1], 10);
        const year = parseInt(dobParts[2], 10);
        
        if (isNaN(first) || isNaN(second) || isNaN(year)) return 40;
        birthDate = new Date(year, first - 1, second);
      } else if (dob.includes('-')) {
        // ISO format - Safari compatible
        const isoDate = dob.includes('T') ? dob.split('T')[0] : dob;
        birthDate = new Date(isoDate + 'T00:00:00');
      } else {
        return 40;
      }

      if (isNaN(birthDate.getTime())) return 40;

      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      const finalAge = Math.max(0, age);
      return isNaN(finalAge) ? 40 : finalAge;
    } catch (error) {
      console.error('Error calculating age:', error);
      return 40;
    }
  };

  // Calculate current policy year from issue date - Safari compatible
  const calculateCurrentPolicyYear = (): number => {
    try {
      const issueDate = (selectedPolicyData as any)?.issueDate || (selectedPolicyData as any)?.policyStartDate;
      if (!issueDate) return 1;

      let issue: Date;
      if (typeof issueDate === 'string') {
        // Handle ISO format - Safari compatible
        const isoDate = issueDate.includes('T') ? issueDate.split('T')[0] : issueDate;
        issue = new Date(isoDate + 'T00:00:00');
      } else {
        issue = new Date(issueDate);
      }

      if (isNaN(issue.getTime())) return 1;

      const today = new Date();
      const yearsDiff = today.getFullYear() - issue.getFullYear();
      const monthsDiff = today.getMonth() - issue.getMonth();
      const daysDiff = today.getDate() - issue.getDate();

      let totalMonths = yearsDiff * 12 + monthsDiff;
      if (daysDiff >= 0) {
        totalMonths += 1;
      }

      const policyYear = Math.max(1, Math.ceil(totalMonths / 12));
      return isNaN(policyYear) ? 1 : policyYear;
    } catch (error) {
      console.error('Error calculating policy year:', error);
      return 1;
    }
  };

  // Get current calendar year - Safari compatible
  const getCurrentYear = (): number => {
    try {
      const currentYear = new Date().getFullYear();
      return isNaN(currentYear) ? 2024 : currentYear;
    } catch (error) {
      console.error('Error getting current year:', error);
      return 2024;
    }
  };

  // Initialize ranges with actual values
  React.useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setFullSurrender(prev => ({
      ...prev,
      ageStart: currentAge,
      policyYearStart: currentPolicyYear,
      calendarYearStart: currentYear
    }));

    setWithdrawalModel(prev => ({
      ...prev,
      flatAnnualAgeRange: {
        start: currentAge,
        end: 100
      },
      flatAnnualPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      flatAnnualCalendarYearRange: {
        start: currentYear,
        end: 2100
      },
      increasingEndAge: currentAge + 20
    }));

    setLoanModel(prev => ({
      ...prev,
      annualAmountAgeRange: {
        start: currentAge,
        end: 100
      },
      annualAmountPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      annualAmountCalendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Generate table data for flat annual section
  const generateFlatAnnualTableData = (): TableRowData[] => {
    const { flatAnnualSelectedTypes, flatAnnualAgeRange, flatAnnualPolicyYearRange, flatAnnualCalendarYearRange } = withdrawalModel;

    let startYear = 0;
    let endYear = 0;

    if (flatAnnualSelectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (flatAnnualAgeRange.start - currentAge);
      endYear = currentCalendarYear + (flatAnnualAgeRange.end - currentAge);
    } else if (flatAnnualSelectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (flatAnnualPolicyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (flatAnnualPolicyYearRange.end - currentPolicyYear);
    } else if (flatAnnualSelectedTypes.calendarYear) {
      startYear = flatAnnualCalendarYearRange.start;
      endYear = flatAnnualCalendarYearRange.end;
    }

    // Return empty array only if no type is selected
    if (!flatAnnualSelectedTypes.age && !flatAnnualSelectedTypes.policyYear && !flatAnnualSelectedTypes.calendarYear) {
      return [];
    }

    // Ensure we have valid year range
    if (startYear === 0 || endYear === 0 || startYear > endYear) return [];

    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    const data: TableRowData[] = [];
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;

    for (let year = startYear; year <= actualEndYear; year++) {
      const row: TableRowData = {
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        amount: parseFloat(withdrawalModel.flatAnnualAmount) || 0
      };
      data.push(row);
    }

    return data;
  };

  // Generate table data for loan annual amount section
  const generateLoanAnnualTableData = (): TableRowData[] => {
    const { annualAmountSelectedTypes, annualAmountAgeRange, annualAmountPolicyYearRange, annualAmountCalendarYearRange } = loanModel;

    let startYear = 0;
    let endYear = 0;

    if (annualAmountSelectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (annualAmountAgeRange.start - currentAge);
      endYear = currentCalendarYear + (annualAmountAgeRange.end - currentAge);
    } else if (annualAmountSelectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (annualAmountPolicyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (annualAmountPolicyYearRange.end - currentPolicyYear);
    } else if (annualAmountSelectedTypes.calendarYear) {
      startYear = annualAmountCalendarYearRange.start;
      endYear = annualAmountCalendarYearRange.end;
    }

    // Return empty array only if no type is selected
    if (!annualAmountSelectedTypes.age && !annualAmountSelectedTypes.policyYear && !annualAmountSelectedTypes.calendarYear) {
      return [];
    }

    // Ensure we have valid year range
    if (startYear === 0 || endYear === 0 || startYear > endYear) return [];

    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();

    const data: TableRowData[] = [];
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;

    for (let year = startYear; year <= actualEndYear; year++) {
      const row: TableRowData = {
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        amount: parseFloat(loanModel.annualAmount) || 0
      };
      data.push(row);
    }

    return data;
  };

  // Update table data when selections change for flat annual
  React.useEffect(() => {
    // Only generate table data if the modify option is enabled
    if (withdrawalModel.modifyFlatAnnualEnabled) {
      const newTableData = generateFlatAnnualTableData();
      setWithdrawalModel(prev => ({ ...prev, flatAnnualTableData: newTableData }));
    }
  }, [withdrawalModel.modifyFlatAnnualEnabled, withdrawalModel.flatAnnualSelectedTypes, withdrawalModel.flatAnnualAgeRange, withdrawalModel.flatAnnualPolicyYearRange, withdrawalModel.flatAnnualCalendarYearRange, withdrawalModel.flatAnnualAmount]);

  // Update table data when selections change for loan annual amount
  React.useEffect(() => {
    // Only generate table data if the modify option is enabled
    if (loanModel.modifyAnnualAmountEnabled) {
      const newTableData = generateLoanAnnualTableData();
      setLoanModel(prev => ({ ...prev, annualAmountTableData: newTableData }));
    }
  }, [loanModel.modifyAnnualAmountEnabled, loanModel.annualAmountSelectedTypes, loanModel.annualAmountAgeRange, loanModel.annualAmountPolicyYearRange, loanModel.annualAmountCalendarYearRange, loanModel.annualAmount]);

  // Save scenario function
  const saveScenario = async () => {
    if (!selectedCustomerData || !selectedPolicyData) {
      showNotification('Please select a customer and policy first!', 'error');
      return;
    }

    setIsSaving(true);
    try {
      // Prepare data for backend API
      const surrenderIncomeData: SurrenderIncomeIllustrationData = {
        policy_id: parseInt(selectedPolicyData.id || selectedCustomerData.policyNumber || '0'),

        // Full Surrender/Income (Question 501)
        income_by_full_surrender: fullSurrender.incomeByFullSurrenderEnabled,
        income_by_full_surrender_amount: fullSurrender.incomeByFullSurrenderAmount ?
          parseFloat(fullSurrender.incomeByFullSurrenderAmount) : undefined,
        modify_full_surrender: fullSurrender.modifyFullSurrenderEnabled,
        modify_full_surrender_type: fullSurrender.selectedType as 'age' | 'policyYear' | 'calendarYear' | undefined,
        modify_full_surrender_age: fullSurrender.selectedType === 'age' ? fullSurrender.ageStart : undefined,
        modify_full_surrender_policy_year: fullSurrender.selectedType === 'policyYear' ? fullSurrender.policyYearStart : undefined,
        modify_full_surrender_calendar_year: fullSurrender.selectedType === 'calendarYear' ? fullSurrender.calendarYearStart : undefined,

        // Withdrawal Model (Question 502)
        fixed_amount_enabled: withdrawalModel.fixedAmountEnabled,
        fixed_amount: withdrawalModel.fixedAmount ? parseFloat(withdrawalModel.fixedAmount) : undefined,
        percent_of_cash_enabled: withdrawalModel.percentOfCashEnabled,
        percent_of_cash: withdrawalModel.percentOfCash ? parseFloat(withdrawalModel.percentOfCash) : undefined,
        flat_annual_enabled: withdrawalModel.flatAnnualEnabled,
        flat_annual_amount: withdrawalModel.flatAnnualAmount ? parseFloat(withdrawalModel.flatAnnualAmount) : undefined,
        increasing_income_enabled: withdrawalModel.increasingIncomeEnabled,
        increasing_start_amount: withdrawalModel.increasingStartAmount ? parseFloat(withdrawalModel.increasingStartAmount) : undefined,
        increasing_percentage: withdrawalModel.increasingPercentage ? parseFloat(withdrawalModel.increasingPercentage) : undefined,
        increasing_end_age: withdrawalModel.increasingEndAge,

        // Modify Withdrawal (Question 503)
        modify_flat_annual_enabled: withdrawalModel.modifyFlatAnnualEnabled,
        modify_flat_annual_type: withdrawalModel.flatAnnualSelectedTypes.age ? 'age' :
          withdrawalModel.flatAnnualSelectedTypes.policyYear ? 'policyYear' :
          withdrawalModel.flatAnnualSelectedTypes.calendarYear ? 'calendarYear' : undefined,
        modify_flat_annual_age_start: withdrawalModel.flatAnnualSelectedTypes.age ? withdrawalModel.flatAnnualAgeRange.start : undefined,
        modify_flat_annual_age_end: withdrawalModel.flatAnnualSelectedTypes.age ? withdrawalModel.flatAnnualAgeRange.end : undefined,
        modify_flat_annual_policy_year_start: withdrawalModel.flatAnnualSelectedTypes.policyYear ? withdrawalModel.flatAnnualPolicyYearRange.start : undefined,
        modify_flat_annual_policy_year_end: withdrawalModel.flatAnnualSelectedTypes.policyYear ? withdrawalModel.flatAnnualPolicyYearRange.end : undefined,
        modify_flat_annual_calendar_year_start: withdrawalModel.flatAnnualSelectedTypes.calendarYear ? withdrawalModel.flatAnnualCalendarYearRange.start : undefined,
        modify_flat_annual_calendar_year_end: withdrawalModel.flatAnnualSelectedTypes.calendarYear ? withdrawalModel.flatAnnualCalendarYearRange.end : undefined,

        // Schedule data if available
        schedule_data: withdrawalModel.flatAnnualTableData.length > 0 ? withdrawalModel.flatAnnualTableData.map(row => ({
          age: row.age,
          policy_year: parseInt(row.policyYear.replace('Year ', '')),
          calendar_year: row.calendarYear,
          withdrawal_amount: row.amount
        })) : undefined
      };

      // Validate data before saving
      const validationErrors = validateSurrenderIncomeData(surrenderIncomeData);
      if (validationErrors.length > 0) {
        showNotification(`Validation errors: ${validationErrors.join(', ')}`, 'error');
        return;
      }

      // Save to backend
      const currentAge = calculateCurrentAge();
      const currentPolicyYear = calculateCurrentPolicyYear();
      const result = await saveSurrenderIncomeIllustration(surrenderIncomeData, currentAge, currentPolicyYear);

      if (result.status === 'SUCCESS') {
        showNotification('Income illustration is saved successfully', 'success');

        // ✅ Reload scenarios from database to get the real saved scenarios with database IDs
        try {
          if (selectedPolicyData?.id) {
            console.log('🔄 Reloading scenarios from database after Income save...');
            await loadScenariosFromBackend(parseInt(selectedPolicyData.id));
            showNotification('Income illustration saved and loaded from database successfully!', 'success');
            console.log('✅ Income scenarios saved and reloaded from database');
          }
        } catch (error) {
          console.error('❌ Error reloading scenarios from database:', error);
          showNotification('Scenarios saved but error loading from database', 'warning');
        }

      } else {
        showNotification(`Failed to save: ${result.message || 'Unknown error'}`, 'error');
      }
    } catch (error) {
      console.error('Error saving surrender/income illustration:', error);
      showNotification('Error saving surrender/income illustration. Please try again.', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Reset all scenario state
  const handleResetScenarios = () => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setFullSurrender({
      incomeByFullSurrenderEnabled: false,
      incomeByFullSurrenderAmount: '',
      modifyFullSurrenderEnabled: false,
      selectedType: '',
      ageStart: currentAge,
      policyYearStart: currentPolicyYear,
      calendarYearStart: currentYear
    });

    setWithdrawalModel({
      fixedAmountEnabled: false,
      fixedAmount: '',
      percentOfCashEnabled: false,
      percentOfCash: '',
      flatAnnualEnabled: false,
      flatAnnualAmount: '',
      modifyFlatAnnualEnabled: false,
      flatAnnualSelectedTypes: {
        age: false,
        policyYear: false,
        calendarYear: false
      },
      flatAnnualAgeRange: {
        start: currentAge,
        end: 100
      },
      flatAnnualPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      flatAnnualCalendarYearRange: {
        start: currentYear,
        end: 2100
      },
      flatAnnualIsEditing: false,
      flatAnnualTableData: [],
      increasingIncomeEnabled: false,
      increasingStartAmount: '',
      increasingPercentage: '',
      increasingEndAge: currentAge + 20,
    });

    setLoanModel({
      fixedAmountEnabled: false,
      fixedAmount: '',
      percentOfCashEnabled: false,
      percentOfCash: '',
      annualAmountEnabled: false,
      annualAmount: '',
      premiumFinancingEnabled: false,
      modifyAnnualAmountEnabled: false,
      annualAmountSelectedTypes: {
        age: false,
        policyYear: false,
        calendarYear: false
      },
      annualAmountAgeRange: {
        start: currentAge,
        end: 100
      },
      annualAmountPolicyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      annualAmountCalendarYearRange: {
        start: currentYear,
        end: 2100
      },
      annualAmountIsEditing: false,
      annualAmountTableData: []
    });

    alert('All income scenarios have been reset!');
  };

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Income illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="min-h-screen bg-gray-50 p-4 space-y-8">
          {/* Top Description */}
          <Card className="mb-8">
            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
              <p className="text-lg text-gray-800 leading-relaxed">
                The income from the policy can be through either a partial withdrawal of cash value or through a new or top-up loan. You can model also a full surrender now or a later date.
              </p>
            </div>
          </Card>

          {/* 1. Full Surrender/Income Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">1. Full surrender/income</h2>
            <div className="space-y-4">
              {/* a. Income by full surrender */}
              <div className="space-y-2">
                <label className="text-lg font-semibold text-black mb-2">
                  Income by full surrender now
                </label>
                <div className="mt-2 ml-6">
                  <Input
                    label="Amount: $"
                    value={fullSurrender.incomeByFullSurrenderAmount}
                    onChange={(e) => setFullSurrender(prev => ({
                      ...prev,
                      incomeByFullSurrenderAmount: e.target.value,
                      incomeByFullSurrenderEnabled: e.target.value !== '',
                      modifyFullSurrenderEnabled: e.target.value !== '' ? false : prev.modifyFullSurrenderEnabled
                    }))}
                    className="text-black placeholder-black"
                    placeholder="Enter amount"
                  />
                </div>
              </div>

              {/* b. Modify full surrender/income */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="radio"
                    checked={fullSurrender.modifyFullSurrenderEnabled}
                    onChange={(e) => setFullSurrender(prev => ({
                      ...prev,
                      modifyFullSurrenderEnabled: prev.modifyFullSurrenderEnabled ? false : true
                    }))}
                    className="mr-2"
                  />
                 Income by full surrender in Future year
                </label>
                {fullSurrender.modifyFullSurrenderEnabled && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-white p-6 rounded-lg border border-gray-300">
                      {/* Type Selection - only one can be selected */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={fullSurrender.selectedType === 'age'}
                            onChange={() => setFullSurrender(prev => ({
                              ...prev,
                              selectedType: prev.selectedType === 'age' ? '' : 'age'
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={fullSurrender.selectedType === 'policyYear'}
                            onChange={() => setFullSurrender(prev => ({
                              ...prev,
                              selectedType: prev.selectedType === 'policyYear' ? '' : 'policyYear'
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={fullSurrender.selectedType === 'calendarYear'}
                            onChange={() => setFullSurrender(prev => ({
                              ...prev,
                              selectedType: prev.selectedType === 'calendarYear' ? '' : 'calendarYear'
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Age Start Toggle Bar - Safari compatible */}
                      {fullSurrender.selectedType === 'age' && (
                        <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    ageStart: Math.max(calculateCurrentAge(), (prev.ageStart || calculateCurrentAge()) - 1)
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.ageStart || calculateCurrentAge()}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    ageStart: Math.min(100, (prev.ageStart || calculateCurrentAge()) + 1)
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Start Toggle Bar - Safari compatible */}
                      {fullSurrender.selectedType === 'policyYear' && (
                        <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    policyYearStart: Math.max(calculateCurrentPolicyYear(), (prev.policyYearStart || calculateCurrentPolicyYear()) - 1)
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.policyYearStart || calculateCurrentPolicyYear()}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    policyYearStart: Math.min(100, (prev.policyYearStart || calculateCurrentPolicyYear()) + 1)
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Calendar Year Start Toggle Bar - Safari compatible */}
                      {fullSurrender.selectedType === 'calendarYear' && (
                        <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    calendarYearStart: Math.max(getCurrentYear(), (prev.calendarYearStart || getCurrentYear()) - 1)
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {fullSurrender.calendarYearStart || getCurrentYear()}
                                </div>
                                <button
                                  onClick={() => setFullSurrender(prev => ({
                                    ...prev,
                                    calendarYearStart: Math.min(2100, (prev.calendarYearStart || getCurrentYear()) + 1)
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* 2. Withdrawal Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">2. Do you want to model policy withdrawals/Income (either one-time or recurring)?</h2>
            <div className="space-y-4">
              {/* a. Fixed amount or percentage */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    checked={withdrawalModel.fixedAmountEnabled || withdrawalModel.percentOfCashEnabled}
                    onChange={e => {
                      const isCurrentlySelected = withdrawalModel.fixedAmountEnabled || withdrawalModel.percentOfCashEnabled;
                      if (!isCurrentlySelected) {
                        // When enabling, default to fixed amount
                        setWithdrawalModel(prev => ({ 
                          ...prev, 
                          fixedAmountEnabled: true,
                          percentOfCashEnabled: false,
                          increasingIncomeEnabled: false,
                          flatAnnualEnabled: false,
                          modifyFlatAnnualEnabled: false
                        }));
                      } else {
                        // When disabling, turn off both
                        setWithdrawalModel(prev => ({ 
                          ...prev, 
                          fixedAmountEnabled: false,
                          percentOfCashEnabled: false
                        }));
                      }
                    }}
                    className="mr-2"
                  />
                   Fixed amount or % of available cash value
                </label>
                {(withdrawalModel.fixedAmountEnabled || withdrawalModel.percentOfCashEnabled) && (
                  <div className="mt-2 ml-6">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-black font-semibold">Fixed amount: $</span>
                        <input
                          type="number"
                      value={withdrawalModel.fixedAmount}
                          onChange={e => {
                            const value = e.target.value;
                            setWithdrawalModel(prev => ({ 
                              ...prev, 
                              fixedAmount: value,
                              fixedAmountEnabled: value !== '',
                              percentOfCashEnabled: value === '' ? prev.percentOfCashEnabled : false,
                              percentOfCash: value !== '' ? '' : prev.percentOfCash
                            }));
                          }}
                          disabled={withdrawalModel.percentOfCashEnabled && withdrawalModel.percentOfCash !== ''}
                          className={`px-3 py-2 border rounded-lg w-32 ${
                            withdrawalModel.percentOfCashEnabled && withdrawalModel.percentOfCash !== ''
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-black border-gray-300'
                          }`}
                      placeholder="Enter amount"
                    />
                  </div>
                      <span className="text-black font-semibold">or</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-black font-semibold">% of available cash value:</span>
                  <input
                          type="number"
                          value={withdrawalModel.percentOfCash}
                    onChange={e => {
                            const value = e.target.value;
                      setWithdrawalModel(prev => ({ 
                        ...prev, 
                              percentOfCash: value,
                              percentOfCashEnabled: value !== '',
                              fixedAmountEnabled: value === '' ? prev.fixedAmountEnabled : false,
                              fixedAmount: value !== '' ? '' : prev.fixedAmount
                      }));
                    }}
                          disabled={withdrawalModel.fixedAmountEnabled && withdrawalModel.fixedAmount !== ''}
                          className={`px-3 py-2 border rounded-lg w-20 ${
                            withdrawalModel.fixedAmountEnabled && withdrawalModel.fixedAmount !== ''
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-black border-gray-300'
                          }`}
                      placeholder="%"
                    />
                    <span className="text-black">% now</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* b. Increasing Income stream */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    checked={withdrawalModel.increasingIncomeEnabled}
                    onChange={e => setWithdrawalModel(prev => ({ 
                      ...prev, 
                      increasingIncomeEnabled: prev.increasingIncomeEnabled ? false : true,
                      fixedAmountEnabled: false,
                      percentOfCashEnabled: false,
                      flatAnnualEnabled: false,
                      modifyFlatAnnualEnabled: false
                    }))}
                    className="mr-2"
                  />
                  Increasing Income stream
                </label>
                {withdrawalModel.increasingIncomeEnabled && (
                  <div className="mt-2 ml-6 space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-black">Starting amount</span>
                        <Input
                          value={withdrawalModel.increasingStartAmount}
                          onChange={e => setWithdrawalModel(prev => ({ ...prev, increasingStartAmount: e.target.value }))}
                          className="text-black placeholder-black"
                          placeholder="Amount"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-black">by</span>
                        <Input
                          value={withdrawalModel.increasingPercentage}
                          onChange={e => setWithdrawalModel(prev => ({ ...prev, increasingPercentage: e.target.value }))}
                          className="text-black placeholder-black w-20"
                          placeholder="%"
                        />
                        <span className="text-black">% every year from now</span>
                      </div>
                    </div>
                    
                    {/* Age Toggle Bar - Safari compatible */}
                    <div className="flex items-center space-x-3">
                      <span className="text-black font-semibold">up to age</span>
                      <div className="flex items-center bg-white border border-gray-300 rounded-lg shadow-sm">
                        <button
                          onClick={() => setWithdrawalModel(prev => ({
                            ...prev,
                            increasingEndAge: Math.max(calculateCurrentAge(), (prev.increasingEndAge || calculateCurrentAge()) - 1)
                          }))}
                          className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                        >
                          ◀
                        </button>
                        <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                          {withdrawalModel.increasingEndAge || calculateCurrentAge()}
                        </div>
                        <button
                          onClick={() => setWithdrawalModel(prev => ({
                            ...prev,
                            increasingEndAge: Math.min(100, (prev.increasingEndAge || calculateCurrentAge()) + 1)
                          }))}
                          className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                        >
                          ▶
                        </button>
                      </div>
                      <span className="text-black">years</span>
                    </div>
                  </div>
                )}
              </div>

              {/* c. Flat Annual Amount */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    checked={withdrawalModel.flatAnnualEnabled}
                    onChange={e => setWithdrawalModel(prev => ({ 
                      ...prev, 
                      flatAnnualEnabled: prev.flatAnnualEnabled ? false : true,
                      fixedAmountEnabled: false,
                      percentOfCashEnabled: false,
                      increasingIncomeEnabled: false,
                      modifyFlatAnnualEnabled: false
                    }))}
                    className="mr-2"
                  />
                 Flat Annual Amount
                </label>
                {withdrawalModel.flatAnnualEnabled && (
                  <div className="mt-2 ml-6">
                    <Input
                      label="Amount: $"
                      value={withdrawalModel.flatAnnualAmount}
                      onChange={e => setWithdrawalModel(prev => ({ ...prev, flatAnnualAmount: e.target.value }))}
                      className="text-black placeholder-black"
                      placeholder="Enter amount"
                    />
                  </div>
                )}
              </div>

              {/* d. Modify the flat annual amount by year */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    checked={withdrawalModel.modifyFlatAnnualEnabled}
                    onChange={(e) => setWithdrawalModel(prev => ({ 
                      ...prev, 
                      modifyFlatAnnualEnabled: prev.modifyFlatAnnualEnabled ? false : true,
                      fixedAmountEnabled: false,
                      percentOfCashEnabled: false,
                      increasingIncomeEnabled: false,
                      flatAnnualEnabled: false
                    }))}
                    className="mr-2"
                  />
                  Modify the flat annual amount by year
                </label>
                {withdrawalModel.modifyFlatAnnualEnabled && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-white p-6 rounded-lg border border-gray-300">
                      {/* Type Selection - only one can be selected */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={withdrawalModel.flatAnnualSelectedTypes.age}
                            onChange={(e) => setWithdrawalModel(prev => ({
                              ...prev,
                              flatAnnualSelectedTypes: {
                                age: e.target.checked,
                                policyYear: false,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={withdrawalModel.flatAnnualSelectedTypes.policyYear}
                            onChange={(e) => setWithdrawalModel(prev => ({
                              ...prev,
                              flatAnnualSelectedTypes: {
                                age: false,
                                policyYear: e.target.checked,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={withdrawalModel.flatAnnualSelectedTypes.calendarYear}
                            onChange={(e) => setWithdrawalModel(prev => ({
                              ...prev,
                              flatAnnualSelectedTypes: {
                                age: false,
                                policyYear: false,
                                calendarYear: e.target.checked
                              }
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Age Range Toggle Bars - Safari compatible */}
                      {withdrawalModel.flatAnnualSelectedTypes.age && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualAgeRange: { 
                                      ...prev.flatAnnualAgeRange, 
                                      start: Math.max(calculateCurrentAge(), (prev.flatAnnualAgeRange.start || calculateCurrentAge()) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {withdrawalModel.flatAnnualAgeRange.start || calculateCurrentAge()}
                                </div>
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualAgeRange: { 
                                      ...prev.flatAnnualAgeRange, 
                                      start: Math.min(100, (prev.flatAnnualAgeRange.start || calculateCurrentAge()) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Age</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualAgeRange: { 
                                      ...prev.flatAnnualAgeRange, 
                                      end: Math.max(prev.flatAnnualAgeRange.start || calculateCurrentAge(), (prev.flatAnnualAgeRange.end || 100) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {withdrawalModel.flatAnnualAgeRange.end || 100}
                                </div>
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualAgeRange: { 
                                      ...prev.flatAnnualAgeRange, 
                                      end: Math.min(100, (prev.flatAnnualAgeRange.end || 100) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Range Toggle Bars - Safari compatible */}
                      {withdrawalModel.flatAnnualSelectedTypes.policyYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualPolicyYearRange: { 
                                      ...prev.flatAnnualPolicyYearRange, 
                                      start: Math.max(calculateCurrentPolicyYear(), (prev.flatAnnualPolicyYearRange.start || calculateCurrentPolicyYear()) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {withdrawalModel.flatAnnualPolicyYearRange.start || calculateCurrentPolicyYear()}
                                </div>
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualPolicyYearRange: { 
                                      ...prev.flatAnnualPolicyYearRange, 
                                      start: Math.min(100, (prev.flatAnnualPolicyYearRange.start || calculateCurrentPolicyYear()) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualPolicyYearRange: { 
                                      ...prev.flatAnnualPolicyYearRange, 
                                      end: Math.max(prev.flatAnnualPolicyYearRange.start || calculateCurrentPolicyYear(), (prev.flatAnnualPolicyYearRange.end || 100) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {withdrawalModel.flatAnnualPolicyYearRange.end || 100}
                                </div>
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualPolicyYearRange: { 
                                      ...prev.flatAnnualPolicyYearRange, 
                                      end: Math.min(100, (prev.flatAnnualPolicyYearRange.end || 100) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Calendar Year Range Toggle Bars - Safari compatible */}
                      {withdrawalModel.flatAnnualSelectedTypes.calendarYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualCalendarYearRange: { 
                                      ...prev.flatAnnualCalendarYearRange, 
                                      start: Math.max(getCurrentYear(), (prev.flatAnnualCalendarYearRange.start || getCurrentYear()) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {withdrawalModel.flatAnnualCalendarYearRange.start || getCurrentYear()}
                                </div>
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualCalendarYearRange: { 
                                      ...prev.flatAnnualCalendarYearRange, 
                                      start: Math.min(2100, (prev.flatAnnualCalendarYearRange.start || getCurrentYear()) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualCalendarYearRange: { 
                                      ...prev.flatAnnualCalendarYearRange, 
                                      end: Math.max(prev.flatAnnualCalendarYearRange.start || getCurrentYear(), (prev.flatAnnualCalendarYearRange.end || 2100) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {withdrawalModel.flatAnnualCalendarYearRange.end || 2100}
                                </div>
                                <button
                                  onClick={() => setWithdrawalModel(prev => ({
                                    ...prev,
                                    flatAnnualCalendarYearRange: { 
                                      ...prev.flatAnnualCalendarYearRange, 
                                      end: Math.min(2100, (prev.flatAnnualCalendarYearRange.end || 2100) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Buttons Row - moved outside container and above table */}
                    <div className="flex justify-between items-center mt-6 mb-4">
                      <button className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                        View Year by Year Details
                      </button>
                      <button
                        onClick={() => setWithdrawalModel(prev => ({ ...prev, flatAnnualIsEditing: !prev.flatAnnualIsEditing }))}
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                      >
                        {withdrawalModel.flatAnnualIsEditing ? 'Lock Schedule' : 'Modify Schedule'}
                      </button>
                    </div>

                    {/* Data Table */}
                    <div className="mt-4">
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                          <thead>
                            <tr className="bg-gray-100">
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {withdrawalModel.flatAnnualTableData.length === 0 ? (
                              <tr>
                                <td colSpan={4} className="border border-gray-300 px-4 py-2 text-center text-gray-500">
                                  {!withdrawalModel.flatAnnualSelectedTypes.age && !withdrawalModel.flatAnnualSelectedTypes.policyYear && !withdrawalModel.flatAnnualSelectedTypes.calendarYear
                                    ? "Select Age, Policy Year, or Calendar Year to populate table"
                                    : "Configure year range to populate table"}
                                </td>
                              </tr>
                            ) : (
                              withdrawalModel.flatAnnualTableData.map((row, index) => (
                                <tr key={index}>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.age}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.policyYear}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.calendarYear}</td>
                                  <td className="border border-gray-300 px-4 py-2">
                                    <input
                                      type="number"
                                      value={row.amount}
                                      readOnly={!withdrawalModel.flatAnnualIsEditing}
                                      onChange={(e) => {
                                        if (withdrawalModel.flatAnnualIsEditing) {
                                          const newTableData = [...withdrawalModel.flatAnnualTableData];
                                          newTableData[index].amount = parseInt(e.target.value) || 0;
                                          setWithdrawalModel(prev => ({ ...prev, flatAnnualTableData: newTableData }));
                                        }
                                      }}
                                      className={`w-full p-2 border rounded text-black ${
                                        withdrawalModel.flatAnnualIsEditing
                                          ? 'border-blue-300 bg-white'
                                          : 'border-gray-300 bg-gray-100'
                                      }`}
                                      step="10000"
                                    />
                                  </td>
                                </tr>
                              ))
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* 3. Policy Loan Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">3. Your Policy is eligible for a loan. Or Do you want to model an income stream using policy loans (e.g., tax-free retirement income strategy)? Do you want us to illustrate?</h2>
            <div className="space-y-4">
              {/* a. Fixed amount or percentage */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    checked={loanModel.fixedAmountEnabled || loanModel.percentOfCashEnabled}
                    onChange={e => {
                      const isCurrentlySelected = loanModel.fixedAmountEnabled || loanModel.percentOfCashEnabled;
                      if (!isCurrentlySelected) {
                        // When enabling, default to fixed amount
                        setLoanModel(prev => ({ 
                          ...prev, 
                          fixedAmountEnabled: true,
                          percentOfCashEnabled: false,
                          premiumFinancingEnabled: false,
                          annualAmountEnabled: false,
                          modifyAnnualAmountEnabled: false
                        }));
                      } else {
                        // When disabling, turn off both
                        setLoanModel(prev => ({ 
                          ...prev, 
                          fixedAmountEnabled: false,
                          percentOfCashEnabled: false
                        }));
                      }
                    }}
                    className="mr-2"
                  />
                  Fixed amount or % of available cash value
                </label>
                {(loanModel.fixedAmountEnabled || loanModel.percentOfCashEnabled) && (
                  <div className="mt-2 ml-6">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-black font-semibold">Fixed amount: $</span>
                        <input
                          type="number"
                          value={loanModel.fixedAmount}
                          onChange={e => {
                            const value = e.target.value;
                            setLoanModel(prev => ({ 
                              ...prev, 
                              fixedAmount: value,
                              fixedAmountEnabled: value !== '',
                              percentOfCashEnabled: value === '' ? prev.percentOfCashEnabled : false,
                              percentOfCash: value !== '' ? '' : prev.percentOfCash
                            }));
                          }}
                          disabled={loanModel.percentOfCashEnabled && loanModel.percentOfCash !== ''}
                          className={`px-3 py-2 border rounded-lg w-32 ${
                            loanModel.percentOfCashEnabled && loanModel.percentOfCash !== ''
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-black border-gray-300'
                          }`}
                          placeholder="Enter amount"
                        />
                      </div>
                      <span className="text-black font-semibold">or</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-black font-semibold">% of available cash value:</span>
                        <input
                          type="number"
                          value={loanModel.percentOfCash}
                          onChange={e => {
                            const value = e.target.value;
                            setLoanModel(prev => ({ 
                              ...prev, 
                              percentOfCash: value,
                              percentOfCashEnabled: value !== '',
                              fixedAmountEnabled: value === '' ? prev.fixedAmountEnabled : false,
                              fixedAmount: value !== '' ? '' : prev.fixedAmount
                            }));
                          }}
                          disabled={loanModel.fixedAmountEnabled && loanModel.fixedAmount !== ''}
                          className={`px-3 py-2 border rounded-lg w-20 ${
                            loanModel.fixedAmountEnabled && loanModel.fixedAmount !== ''
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                              : 'bg-white text-black border-gray-300'
                          }`}
                          placeholder="%"
                        />
                        <span className="text-black">% now</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* b. Loan for paying the premium */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="radio"
                    checked={loanModel.premiumFinancingEnabled}
                    onChange={e => setLoanModel(prev => ({ 
                      ...prev, 
                      premiumFinancingEnabled: prev.premiumFinancingEnabled ? false : true,
                      fixedAmountEnabled: false,
                      percentOfCashEnabled: false,
                      annualAmountEnabled: false,
                      modifyAnnualAmountEnabled: false
                    }))}
                    className="mr-2"
                  />
                  Loan for paying the premium (Premium financing)
                </label>
              </div>

              {/* c. Annual amount */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    checked={loanModel.annualAmountEnabled}
                    onChange={e => setLoanModel(prev => ({ 
                      ...prev, 
                      annualAmountEnabled: prev.annualAmountEnabled ? false : true,
                      fixedAmountEnabled: false,
                      percentOfCashEnabled: false,
                      premiumFinancingEnabled: false,
                      modifyAnnualAmountEnabled: false
                    }))}
                    className="mr-2"
                  />
                   Annual amount
                </label>
                {loanModel.annualAmountEnabled && (
                  <div className="mt-2 ml-6">
                    <Input
                      value={loanModel.annualAmount}
                      onChange={e => setLoanModel(prev => ({ ...prev, annualAmount: e.target.value }))}
                      className="text-black placeholder-black"
                      placeholder="Enter amount"
                    />
                  </div>
                )}
              </div>

              {/* d. Modify the annual amount by year */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black">
                  <input
                    type="radio"
                    checked={loanModel.modifyAnnualAmountEnabled}
                    onChange={(e) => setLoanModel(prev => ({ 
                      ...prev, 
                      modifyAnnualAmountEnabled: prev.modifyAnnualAmountEnabled ? false : true,
                      fixedAmountEnabled: false,
                      percentOfCashEnabled: false,
                      premiumFinancingEnabled: false,
                      annualAmountEnabled: false
                    }))}
                    className="mr-2"
                  />
                  Modify the annual amount by year
                </label>
                {loanModel.modifyAnnualAmountEnabled && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-white p-6 rounded-lg border border-gray-300">
                      {/* Type Selection - only one can be selected */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={loanModel.annualAmountSelectedTypes.age}
                            onChange={(e) => setLoanModel(prev => ({
                              ...prev,
                              annualAmountSelectedTypes: {
                                age: e.target.checked,
                                policyYear: false,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={loanModel.annualAmountSelectedTypes.policyYear}
                            onChange={(e) => setLoanModel(prev => ({
                              ...prev,
                              annualAmountSelectedTypes: {
                                age: false,
                                policyYear: e.target.checked,
                                calendarYear: false
                              }
                            }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="radio"
                            checked={loanModel.annualAmountSelectedTypes.calendarYear}
                            onChange={(e) => setLoanModel(prev => ({
                              ...prev,
                              annualAmountSelectedTypes: {
                                age: false,
                                policyYear: false,
                                calendarYear: e.target.checked
                              }
                            }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Age Range Toggle Bars - Safari compatible */}
                      {loanModel.annualAmountSelectedTypes.age && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountAgeRange: { 
                                      ...prev.annualAmountAgeRange, 
                                      start: Math.max(calculateCurrentAge(), (prev.annualAmountAgeRange.start || calculateCurrentAge()) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {loanModel.annualAmountAgeRange.start || calculateCurrentAge()}
                                </div>
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountAgeRange: { 
                                      ...prev.annualAmountAgeRange, 
                                      start: Math.min(100, (prev.annualAmountAgeRange.start || calculateCurrentAge()) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Age</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountAgeRange: { 
                                      ...prev.annualAmountAgeRange, 
                                      end: Math.max(prev.annualAmountAgeRange.start || calculateCurrentAge(), (prev.annualAmountAgeRange.end || 100) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {loanModel.annualAmountAgeRange.end || 100}
                                </div>
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountAgeRange: { 
                                      ...prev.annualAmountAgeRange, 
                                      end: Math.min(100, (prev.annualAmountAgeRange.end || 100) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Range Toggle Bars - Safari compatible */}
                      {loanModel.annualAmountSelectedTypes.policyYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountPolicyYearRange: { 
                                      ...prev.annualAmountPolicyYearRange, 
                                      start: Math.max(calculateCurrentPolicyYear(), (prev.annualAmountPolicyYearRange.start || calculateCurrentPolicyYear()) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {loanModel.annualAmountPolicyYearRange.start || calculateCurrentPolicyYear()}
                                </div>
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountPolicyYearRange: { 
                                      ...prev.annualAmountPolicyYearRange, 
                                      start: Math.min(100, (prev.annualAmountPolicyYearRange.start || calculateCurrentPolicyYear()) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountPolicyYearRange: { 
                                      ...prev.annualAmountPolicyYearRange, 
                                      end: Math.max(prev.annualAmountPolicyYearRange.start || calculateCurrentPolicyYear(), (prev.annualAmountPolicyYearRange.end || 100) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {loanModel.annualAmountPolicyYearRange.end || 100}
                                </div>
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountPolicyYearRange: { 
                                      ...prev.annualAmountPolicyYearRange, 
                                      end: Math.min(100, (prev.annualAmountPolicyYearRange.end || 100) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Calendar Year Range Toggle Bars - Safari compatible */}
                      {loanModel.annualAmountSelectedTypes.calendarYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountCalendarYearRange: { 
                                      ...prev.annualAmountCalendarYearRange, 
                                      start: Math.max(getCurrentYear(), (prev.annualAmountCalendarYearRange.start || getCurrentYear()) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {loanModel.annualAmountCalendarYearRange.start || getCurrentYear()}
                                </div>
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountCalendarYearRange: { 
                                      ...prev.annualAmountCalendarYearRange, 
                                      start: Math.min(2100, (prev.annualAmountCalendarYearRange.start || getCurrentYear()) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                              <div className="flex items-center bg-white border border-gray-300 rounded-lg">
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountCalendarYearRange: { 
                                      ...prev.annualAmountCalendarYearRange, 
                                      end: Math.max(prev.annualAmountCalendarYearRange.start || getCurrentYear(), (prev.annualAmountCalendarYearRange.end || 2100) - 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-l-lg"
                                >
                                  ◀
                                </button>
                                <div className="px-6 py-3 text-lg font-semibold text-gray-900 bg-gray-50">
                                  {loanModel.annualAmountCalendarYearRange.end || 2100}
                                </div>
                                <button
                                  onClick={() => setLoanModel(prev => ({
                                    ...prev,
                                    annualAmountCalendarYearRange: { 
                                      ...prev.annualAmountCalendarYearRange, 
                                      end: Math.min(2100, (prev.annualAmountCalendarYearRange.end || 2100) + 1) 
                                    }
                                  }))}
                                  className="px-4 py-3 text-lg text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-r-lg"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Buttons Row - moved outside container and above table */}
                    <div className="flex justify-between items-center mt-6 mb-4">
                      <button className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                        View Year by Year Details
                      </button>
                      <button
                        onClick={() => setLoanModel(prev => ({ ...prev, annualAmountIsEditing: !prev.annualAmountIsEditing }))}
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                      >
                        {loanModel.annualAmountIsEditing ? 'Lock Schedule' : 'Modify Schedule'}
                      </button>
                    </div>

                    {/* Data Table */}
                    <div className="mt-4">
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                          <thead>
                            <tr className="bg-gray-100">
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Amount</th>
                            </tr>
                          </thead>
                          <tbody>
                            {loanModel.annualAmountTableData.length === 0 ? (
                              <tr>
                                <td colSpan={4} className="border border-gray-300 px-4 py-2 text-center text-gray-500">
                                  {!loanModel.annualAmountSelectedTypes.age && !loanModel.annualAmountSelectedTypes.policyYear && !loanModel.annualAmountSelectedTypes.calendarYear
                                    ? "Select Age, Policy Year, or Calendar Year to populate table"
                                    : "Configure year range to populate table"}
                                </td>
                              </tr>
                            ) : (
                              loanModel.annualAmountTableData.map((row, index) => (
                                <tr key={index}>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.age}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.policyYear}</td>
                                  <td className="border border-gray-300 px-4 py-2 text-black">{row.calendarYear}</td>
                                  <td className="border border-gray-300 px-4 py-2">
                                    <input
                                      type="number"
                                      value={row.amount}
                                      readOnly={!loanModel.annualAmountIsEditing}
                                      onChange={(e) => {
                                        if (loanModel.annualAmountIsEditing) {
                                          const newTableData = [...loanModel.annualAmountTableData];
                                          newTableData[index].amount = parseInt(e.target.value) || 0;
                                          setLoanModel(prev => ({ ...prev, annualAmountTableData: newTableData }));
                                        }
                                      }}
                                      className={`w-full p-2 border rounded text-black ${
                                        loanModel.annualAmountIsEditing
                                          ? 'border-blue-300 bg-white'
                                          : 'border-gray-300 bg-gray-100'
                                      }`}
                                      step="10000"
                                    />
                                  </td>
                                </tr>
                              ))
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Card>

          {/* Save and Reset Buttons */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button onClick={saveScenario}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold"
              disabled={isSaving}>
              <Save className="w-4 h-4" />
              <span>{isSaving ? 'Saving...' : 'Save Income Analysis'}</span>
            </Button>
            <Button onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default IncomePage;